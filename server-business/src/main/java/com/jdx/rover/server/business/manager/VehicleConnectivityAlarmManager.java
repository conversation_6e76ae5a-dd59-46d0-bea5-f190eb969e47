/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.manager;

import cn.hutool.core.collection.CollUtil;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.ConnectivityDTO;
import com.jdx.rover.server.api.domain.enums.connectivity.ConnectivityTypeEnum;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 车辆连通性告警管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleConnectivityAlarmManager {

    private final VehicleAlarmManager vehicleAlarmManager;
    private final ReportAlarmRepository reportAlarmRepository;
    private final RedissonRepository redissonRepository;

    /**
     * Redis List key前缀 - 连通性延时记录
     */
    private static final String REDIS_KEY_CONNECTIVITY_DELAY = "connectivity_delay:";

    /**
     * 连续异常次数阈值
     */
    private static final int ABNORMAL_COUNT_THRESHOLD = 3;

    /**
     * 连续正常次数阈值（用于消除告警）
     */
    private static final int NORMAL_COUNT_THRESHOLD = 2;

    /**
     * List最大长度
     */
    private static final int MAX_LIST_SIZE = 10;

    /**
     * List过期时间（秒）
     */
    private static final long LIST_EXPIRE_TIME = 3600;

    /**
     * 处理连通性异常告警
     *
     * @param vehicleName 车辆名称
     * @param connectivityList 连通性数据列表
     * @param recordTime 记录时间
     */
    public void handleConnectivityAlarm(String vehicleName, List<ConnectivityDTO> connectivityList, Date recordTime) {
        if (CollectionUtils.isEmpty(connectivityList)) {
            return;
        }

        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        VehicleAlarmEventDTO alarmDb = alarmMapDb.get(AlarmTypeEnum.CONNECTIVITY_FAILURE.getValue());
        
        boolean shouldGenerateAlarm = false;
        StringBuilder errorMessage = new StringBuilder();

        // 检查各种连通性类型的延时情况
        for (ConnectivityDTO connectivity : connectivityList) {
            ConnectivityTypeEnum typeEnum = ConnectivityTypeEnum.of(connectivity.getConnectivityType());
            if (typeEnum == null || !typeEnum.needAlarmCheck()) {
                continue;
            }

            String redisKey = getRedisKey(vehicleName, typeEnum);
            
            // 记录当前延时到Redis List
            recordDelayToRedis(redisKey, connectivity.getDelay());
            
            // 检查是否满足告警条件
            if (checkAlarmCondition(redisKey, typeEnum.getDelayThreshold())) {
                shouldGenerateAlarm = true;
                errorMessage.append(typeEnum.getTitle())
                          .append("连续")
                          .append(ABNORMAL_COUNT_THRESHOLD)
                          .append("次ping延时大于")
                          .append(typeEnum.getDelayThreshold())
                          .append("ms；");
                log.info("【连通性异常】{} {} 连续{}次ping延时大于{}ms", 
                        vehicleName, typeEnum.getTitle(), ABNORMAL_COUNT_THRESHOLD, typeEnum.getDelayThreshold());
            }
        }

        // 生成告警
        if (shouldGenerateAlarm && Objects.isNull(alarmDb)) {
            VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
            alarm.setType(AlarmTypeEnum.CONNECTIVITY_FAILURE.getValue());
            alarm.setReportTime(recordTime);
            alarm.setErrorCode(AlarmTypeEnum.CONNECTIVITY_FAILURE.getValue());
            alarm.setErrorMessage(errorMessage.toString());
            vehicleAlarmManager.addAlarm(vehicleName, alarm);
            log.info("【连通性异常】生成连通性失败告警，车辆：{}，原因：{}", vehicleName, errorMessage);
            return;
        }

        // 检查告警消除条件
        if (!Objects.isNull(alarmDb)) {
            boolean shouldRemoveAlarm = checkAlarmRemovalConditions(vehicleName, connectivityList);
            if (shouldRemoveAlarm) {
                vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.CONNECTIVITY_FAILURE.getValue());
                log.info("【连通性异常】消除连通性失败告警，车辆：{}", vehicleName);
            }
        }
    }

    /**
     * 检查告警消除条件
     *
     * @param vehicleName 车辆名称
     * @param connectivityList 连通性数据列表
     * @return true-应该消除告警，false-不应该消除告警
     */
    private boolean checkAlarmRemovalConditions(String vehicleName, List<ConnectivityDTO> connectivityList) {
        if (CollectionUtils.isEmpty(connectivityList)) {
            return false;
        }

        // 检查所有需要检查的连通性类型是否都满足消除条件
        for (ConnectivityTypeEnum typeEnum : ConnectivityTypeEnum.values()) {
            if (!typeEnum.needAlarmCheck()) {
                continue;
            }

            String redisKey = getRedisKey(vehicleName, typeEnum);
            
            // 如果任何一个类型不满足消除条件，则不消除告警
            if (!checkRemovalCondition(redisKey, typeEnum.getDelayThreshold())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查单个连通性类型的告警条件
     *
     * @param redisKey Redis键
     * @param threshold 延时阈值
     * @return true-满足告警条件，false-不满足告警条件
     */
    private boolean checkAlarmCondition(String redisKey, Float threshold) {
        int listSize = redissonRepository.getListSize(redisKey);

        if (listSize < ABNORMAL_COUNT_THRESHOLD) {
            return false;
        }

        // 检查最近3次是否都超过阈值
        List<Float> recentDelays = redissonRepository.getListRange(redisKey, -ABNORMAL_COUNT_THRESHOLD, -1);
        for (Float delay : recentDelays) {
            if (delay == null || delay <= threshold) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查单个连通性类型的消除条件
     *
     * @param redisKey Redis键
     * @param threshold 延时阈值
     * @return true-满足消除条件，false-不满足消除条件
     */
    private boolean checkRemovalCondition(String redisKey, Float threshold) {
        int listSize = redissonRepository.getListSize(redisKey);

        if (listSize < NORMAL_COUNT_THRESHOLD) {
            return false;
        }

        // 检查最近2次是否都小于等于阈值
        List<Float> recentDelays = redissonRepository.getListRange(redisKey, -NORMAL_COUNT_THRESHOLD, -1);
        for (Float delay : recentDelays) {
            if (delay == null || delay > threshold) {
                return false;
            }
        }

        return true;
    }

    /**
     * 记录延时到Redis List
     *
     * @param redisKey Redis键
     * @param delay 延时值
     */
    private void recordDelayToRedis(String redisKey, Float delay) {
        if (delay == null) {
            return;
        }

        // 添加新的延时记录
        redissonRepository.addToList(redisKey, delay);

        // 保持List大小不超过最大限制
        while (redissonRepository.getListSize(redisKey) > MAX_LIST_SIZE) {
            redissonRepository.removeFromList(redisKey, 0);
        }

        // 设置过期时间
        redissonRepository.expireList(redisKey, LIST_EXPIRE_TIME);
    }

    /**
     * 获取Redis键
     *
     * @param vehicleName 车辆名称
     * @param typeEnum 连通性类型
     * @return Redis键
     */
    private String getRedisKey(String vehicleName, ConnectivityTypeEnum typeEnum) {
        return REDIS_KEY_CONNECTIVITY_DELAY + vehicleName + ":" + typeEnum.getValue();
    }
}
