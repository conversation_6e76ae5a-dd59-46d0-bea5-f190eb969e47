/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.jmq;

import com.jd.jmq.client.producer.AsyncSendCallback;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.jmq.common.network.command.Command;
import com.jd.jmq.common.network.command.PutMessage;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttToJmqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发送jmq消息manager
 *
 * <AUTHOR>
 * @date 2024/9/29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class JmqProducerManager {
    /**
     * jmq生产者
     */
    private final Producer producer;

    /**
     * 发送jmq消息列表
     */
    public void sendList(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        try {
            producer.send(messageList);
            log.info("发送jmq消息列表成功,{}", messageList.stream().map(Message::getText).collect(Collectors.toList()));
        } catch (JMQException e) {
            log.error("发送jmq消息列表失败,{}", JsonUtils.writeValueAsString(messageList), e);
        }
    }

    /**
     * 发送有序jmq字节数组
     */
    public void sendByteOrdered(String topic, byte[] body, String businessId) {
        Message jmqMsg = new Message();
        jmqMsg.setTopic(topic);
        jmqMsg.setBody(body);
        jmqMsg.setBusinessId(businessId);
        this.sendOrdered(jmqMsg);
    }

    /**
     * 发送无日志有序jmq字符串消息
     */
    public void sendOrdered(String topic, String text, String businessId) {
        Message jmqMsg = new Message(topic, text, businessId);
        this.sendOrdered(jmqMsg);
    }

    /**
     * 发送有序jmq消息
     */
    public void sendOrderedAndLog(String topic, String text, String businessId) {
        Message jmqMsg = new Message(topic, text, businessId);
        this.sendOrderedAndLog(jmqMsg);
    }

    /**
     * 发送有序jmq消息
     */
    public void sendOrderedAndLog(Message message) {
        if (Objects.isNull(message)) {
            return;
        }
        message.setOrdered(true);
        sendAndLog(message);
    }

    /**
     * 发送jmq消息
     */
    public void sendOrdered(Message message) {
        if (Objects.isNull(message)) {
            return;
        }
        message.setOrdered(true);
        send(message);
    }

    /**
     * 发送jmq消息
     */
    public void send(Message message) {
        try {
            producer.send(message);
        } catch (JMQException e) {
            log.error("发送jmq消息失败{},{}", message.getTopic(), message.getText(), e);
        }
    }

    /**
     * 异步发送jmq消息
     */
    public void sendAsync(Message message) {
        try {
            producer.send(message, new AsyncSendCallback() {
                @Override
                public void success(PutMessage putMessage, Command response) {
                }

                @Override
                public void execption(PutMessage putMessage, Throwable cause) {
                    log.error("异步发送jmq消息失败!{}", putMessage, cause);
                }
            });
        } catch (JMQException e) {
            log.error("异步发送jmq消息失败{},{}", message.getTopic(), message.getText(), e);
        }
    }

    /**
     * 异步发送jmq消息列表
     */
    public void sendAsyncList(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        try {
            producer.send(messageList, new AsyncSendCallback() {
                @Override
                public void success(PutMessage putMessage, Command response) {
                }

                @Override
                public void execption(PutMessage putMessage, Throwable cause) {
                    log.error("异步发送jmq消息失败!{}", putMessage, cause);
                }
            });
        } catch (JMQException e) {
            log.error("异步发送jmq消息失败{}", messageList.get(0).getTopic(), e);
        }
    }

    /**
     * 发送jmq消息
     */
    public void sendAndLog(Message message) {
        try {
            producer.send(message);
            log.info("发送jmq消息成功{},{}", message.getTopic(), message.getText());
        } catch (JMQException e) {
            log.error("发送jmq消息失败{},{}", message.getTopic(), message.getText(), e);
        }
    }

    /**
     * 将消息发送到JMQ队列
     *
     * @param mqttTopic  MQTT主题
     * @param payload    消息载荷
     * @param jmqTopic   JMQ主题
     * @param businessId 业务ID
     * @throws Exception 消息发送异常
     */
    public void sendMqttBytesToJmq(String mqttTopic, byte[] payload, String jmqTopic, String businessId) throws Exception {
        // 构建MQTT到JMQ的转换对象
        MqttToJmqDTO<byte[]> mqttToJmqMessage = new MqttToJmqDTO<>(mqttTopic, payload);

        // 构建JMQ消息
        Message jmqMsg = new Message();
        jmqMsg.setTopic(jmqTopic);
        jmqMsg.setBody(JsonUtils.getObjectMapper().writeValueAsBytes(mqttToJmqMessage));
        jmqMsg.setBusinessId(businessId);
        this.sendOrdered(jmqMsg);
    }
}
