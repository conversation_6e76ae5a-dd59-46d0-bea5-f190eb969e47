/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.video;

import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 视频网络情况
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MqttVideoNetListener {
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 订阅视频网络情况消息
     */
    @MqttListener(topics = {"r/video/net/+"}, shareGroup = MqttTopicConstant.SHARE_GROUP)
    public void sendVideoNet(MqttMessageDTO<String> message) {
        try {
            log.info("收到视频网络情况上报,topic={},payload={}", message.getTopic(), message.getPayload());
            jmqProducerManager.sendOrdered(producerTopicProperties.getMqttVideoNet(), message.getPayload(), message.getTopic());
        } catch (Exception e) {
            log.error("mqtt连接发送jmq失败,topic={},payload={}", message.getTopic(), message.getPayload(), e);
        }
    }
}
