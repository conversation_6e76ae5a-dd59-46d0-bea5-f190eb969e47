/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 生产主题配置类
 * 读取application.yml中的jmq.topic.producer属性文件
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
@ConfigurationProperties(prefix = "jmq.topic.producer")
@Data
public class ProducerTopicProperties {
    /**
     * json属性
     */
    private String mqttJsonProperty;
    /**
     * json事件
     */
    private String mqttJsonEvents;
    /**
     * json服务
     */
    private String mqttJsonServices;
    /**
     * pb属性
     */
    private String mqttPbProperty;
    /**
     * pb事件
     */
    private String mqttPbEvents;
    /**
     * pb服务
     */
    private String mqttPbServices;
    /**
     * 断开mqtt
     */
    private String mqttSystemDisconnected;
    /**
     * 连接mqtt
     */
    private String mqttSystemConnected;
    /**
     * 安卓遗嘱
     */
    private String mqttAndroidWill;
    /**
     * 平行驾驶连接回复
     */
    private String replyRDriveControlConnectServer;
    /**
     * 安卓指令回复
     */
    private String mqttReplyAndroidCommand;
    /**
     * 安卓消息
     */
    private String mqttAndroidInfo;
    /**
     * 地图ota进度
     */
    private String mqttOtaProgress;
    /**
     * pdu遗嘱
     */
    private String rPduWill;
    /**
     * 中控前端遗嘱
     */
    private String rDriveWill;
    /**
     * json协议ota
     */
    private String mqttJsonOta;
    /**
     * 视频网络情况
     */
    private String mqttVideoNet;
    /**
     * 大屏视频数据上传
     */
    private String mqttScreenDataVideo;
    /**
     * 大屏视频LocalView上传
     */
    private String mqttScreenDataLocalView;
    /**
     * mqtt驾舱指令
     */
    private String mqttCockpitCommand;
    /**
     * 车身域硬件属性
     */
    private String mqttHardwareProperty;
    /**
     * 车身域硬件状态
     */
    private String mqttHardwareData;
    /**
     * 车身域硬件指令回复
     */
    private String mqttHardwareServicesReply;
    /**
     * 平行驾驶指令回复
     */
    private String driveServerVehicleReply;
}