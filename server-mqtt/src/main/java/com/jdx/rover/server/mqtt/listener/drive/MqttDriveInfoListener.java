/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import cn.hutool.core.util.HexUtil;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import jdx.rover.remote.drive.control.proto.DriveControlDto;
import jdx.rover.remote.drive.vehicle.proto.DriveDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * mqtt远程遥控连接消息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MqttDriveInfoListener {
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    private final Map<String, List<Message>> cockpitListMap = new ConcurrentHashMap<>();

    /**
     * 平行驾驶驾驶舱指令
     *
     * @param message
     */
    @MqttListener(topics = {"r/drive/cockpit/command/#","drive/cockpit/command/+"}, qos = 0
            , serializerType = MqttSerializerType.BYTE, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void cockpitCommand(MqttMessageDTO<byte[]> message) {
        try {
            DriveControlDto.ControlCommand dto = DriveControlDto.ControlCommand.parseFrom(message.getPayload());
            String cockpitNumber = dto.getCockpitNumber();
            String jsonStr = ProtoUtils.protoToJson(dto);
            List<Message> messageList = cockpitListMap.computeIfAbsent(cockpitNumber, k -> new ArrayList<>());
            if (!Objects.equals(dto.getRequestHeader().getClientName(), cockpitNumber)) {
                messageList.add(new Message(producerTopicProperties.getMqttCockpitCommand(), jsonStr));
                if (messageList.size() > 20) {
                    sendMessageBatch(messageList);
                }
            } else if (!messageList.isEmpty()) {
                sendMessageBatch(messageList);
            }
            log.info("receive topic={},payload={}", message.getTopic(), jsonStr);
        } catch (Exception e) {
            log.error("hand drive cockpit command info error topic={},payload={}", message.getTopic()
                    , HexUtil.encodeHexStr(message.getPayload()), e.getMessage());
        }
    }

    /**
     * 发送消息批次
     *
     * @param messageList 消息列表
     */
    private void sendMessageBatch(List<Message> messageList) {
        if (messageList.isEmpty()) {
            return;
        }
        // 创建消息副本，避免并发修改
        List<Message> messagesToSendList = new ArrayList<>(messageList);
        messageList.clear();

        // 异步发送消息批次
        jmqProducerManager.sendAsyncList(messagesToSendList);
    }

    /**
     * 平行驾驶车端状态信息群发
     *
     * @param message
     */
    @MqttListener(topics = {"r/drive/vehicle/group/info/#","drive/vehicle/data/+"}, qos = 0
            , serializerType = MqttSerializerType.BYTE, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void vehicleInfo(MqttMessageDTO<byte[]> message) {
        try {
            DriveDataDto.DriveDataDTO dto = DriveDataDto.DriveDataDTO.parseFrom(message.getPayload());
            log.info("receive topic={},payload={}", message.getTopic(), ProtoUtils.protoToJson(dto));
        } catch (Exception e) {
            log.error("hand drive vehicle info error topic={}, payload={}, error={}", message.getTopic()
                    , HexUtil.encodeHexStr(message.getPayload()), e.getMessage());
        }
    }
}
