/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import com.fasterxml.jackson.databind.JsonNode;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 大屏数据上传
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MqttScreenDataListener {
    private static final String VIDEO_DATA = "VIDEO_DATA";
    private static final String LOCAL_VIEW_DATA = "LOCAL_VIEW_DATA";
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 大屏数据上传
     *
     * @param message
     */
    @MqttListener(topics = {"r/drive/screen/data/#","r/+/drive/screen_data/+"}, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void screenData(MqttMessageDTO<String> message) {
        log.info("收到大屏mqtt上传消息,topic={},payload={}", message.getTopic(), message.getPayload());
        JsonNode jsonNode = JsonUtils.readTree(message.getPayload());
        if (Objects.isNull(jsonNode)) {
            return;
        }
        JsonNode header = jsonNode.get("header");
        if (Objects.isNull(header)) {
            return;
        }
        String messageType = header.get("messageType").asText();
        String data = jsonNode.get("data").toString();
        if (Objects.equals(messageType, VIDEO_DATA)) {
            jmqProducerManager.sendOrdered(producerTopicProperties.getMqttScreenDataVideo(), data, message.getTopic());
        } else if (Objects.equals(messageType, LOCAL_VIEW_DATA)) {
            jmqProducerManager.sendOrdered(producerTopicProperties.getMqttScreenDataLocalView(), data, message.getTopic());
        }
    }
}
