server:
  tomcat:
    max-threads: 800
feign:
  httpclient:
    enabled: true
    max-connections: 800
    max-connections-per-route: 600
spring:
  application:
    name: rover-server-mqtt
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
  kafka:
    producer:
      retries: 5
    consumer:
      group-id: ${spring.application.name}
    listener:
      type: batch
      concurrency: 3
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
logging:
  config: classpath:log4j2.xml

websocket:
  maxTextMessageBufferSize: 81920
  maxBinaryMessageBufferSize: 8192000
  maxSessionIdleTimeout: 600000

rover:
  mqtt:
    primary: jdxmqtt
    broker:
      jdxmqtt:
        url: ssl://jdxmqtt-beta.jdl.com:2000
        clientId: server
        username: emqx_server
        password: emqx_server@jdlX2022
        shareGroupSuffix: ${spring.profiles.active}
        keepAliveInterval: 5
        cleanStart: true
      vehiclemqtt:
        url: ssl://jdxmqtt-beta.jdl.com:2000
        clientId: server
        username: emqx_server
        password: emqx_server@jdlX2022
        shareGroupSuffix: ${spring.profiles.active}
        keepAliveInterval: 5
        cleanStart: true

project:
  redisson:
    timeout: 10000
    subscriptionsPerConnection: 5000
    subscriptionConnectionPoolSize: 5000
    connectionPoolSize: 500
    nettyThreads: 256

jmq:
  address: test-nameserver.jmq.jd.local:50088
  password: 3f9e1de79acd4e3ba7e1e1aec51bf274
  app: roverServer
  topic:
    suffix: _${spring.profiles.active}
    producer:
      mqttJsonProperty: mqtt_json_property${jmq.topic.suffix}
      mqttJsonEvents: mqtt_json_events${jmq.topic.suffix}
      mqttJsonServices: mqtt_json_services${jmq.topic.suffix}
      mqttPbProperty: mqtt_pb_property${jmq.topic.suffix}
      mqttPbEvents: mqtt_pb_events${jmq.topic.suffix}
      mqttPbServices: mqtt_pb_services${jmq.topic.suffix}
      mqttSystemDisconnected: mqtt_system_disconnected${jmq.topic.suffix}
      mqttSystemConnected: mqtt_system_connected${jmq.topic.suffix}
      mqttAndroidWill: mqtt_android_will${jmq.topic.suffix}
      replyRDriveControlConnectServer: reply_r_drive_control_connect_server${jmq.topic.suffix}
      mqttReplyAndroidCommand: mqtt_reply_android_command${jmq.topic.suffix}
      mqttAndroidInfo: mqtt_android_info${jmq.topic.suffix}
      mqttOtaProgress: mqtt_ota_progress${jmq.topic.suffix}
      rPduWill: r_pdu_will${jmq.topic.suffix}
      rDriveWill: r_drive_will${jmq.topic.suffix}
      mqttJsonOta: mqtt_json_ota${jmq.topic.suffix}
      mqttVideoNet: mqtt_video_net${jmq.topic.suffix}
      mqttScreenDataVideo: mqtt_screen_data_video${jmq.topic.suffix}
      mqttScreenDataLocalView: mqtt_screen_data_local_view${jmq.topic.suffix}
      mqttCockpitCommand: mqtt_cockpit_command${jmq.topic.suffix}
      mqttHardwareProperty: mqtt_hardware_property${jmq.topic.suffix}
      mqttHardwareData: mqtt_hardware_data${jmq.topic.suffix}
      mqttHardwareServicesReply: mqtt_hardware_services_reply${jmq.topic.suffix}
      mqttDriveVehicleData: mqtt_drive_vehicle_data${jmq.topic.suffix}
      mqttDriveVehicleServicesReply: mqtt_drive_vehicle_services_reply${jmq.topic.suffix}
      mqttDriveCockpitServicesReply: mqtt_drive_cockpit_services_reply${jmq.topic.suffix}
      driveServerVehicleReply: drive_server_vehicle_reply${jmq.topic.suffix}
    consumer:
      mqtt_send_string: mqtt_send_string${jmq.topic.suffix}
      mqtt_send_byte: mqtt_send_byte${jmq.topic.suffix}