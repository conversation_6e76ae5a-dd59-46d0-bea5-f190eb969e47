/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.enums.LocalViewCtrlType;
import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;
import com.jdx.rover.server.api.domain.enums.SeverErrorEnum;
import com.jdx.rover.server.api.domain.enums.light.SwitchTypeEnum;
import com.jdx.rover.server.api.domain.vo.PassNoSignalIntersectionCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteTrafficLightCommandVO;
import com.jdx.rover.server.api.domain.vo.MaxVelocityCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchLightCommandVO;
import com.jdx.rover.server.api.domain.vo.LocalViewCtrlCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchVehicleModeCommandVO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.entity.CommandMessage;
import com.jdx.rover.server.domain.entity.VehicleManualRebootEntity;
import com.jdx.rover.server.domain.enums.RebootTypeEnum;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import jdx.rover.idl.proto.RemoteServiceEntity;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * This is api interface for mini monitor vehicle command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class RemoteCommandService {

  @Autowired
  private RedissonClient redissonClient;

  private static final String SERVER_NAME = "rover-server";

  /**
   * <p>
   * Post emergency stop command.
   * </p>
   */
  public HttpResult publishEmergencyStopCommand(RemoteCommandVO remoteCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "remoteCommandVo");
    log.info("Receive emergency stop command {}.", remoteCommandVo);
    if (!checkHermesConnection(remoteCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.VehicleEmergencyStopCommand.Builder emergencyStopCommandBuilder =
        RemoteServiceEntity.VehicleEmergencyStopCommand.newBuilder();
    emergencyStopCommandBuilder.setEmergencyStopCommand(true);
    RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder =
        RemoteServiceEntity.ChassisCommand.newBuilder();
    chassisCommandBuilder.setEnableCommand(true);
    chassisCommandBuilder.setEmergencyStop(emergencyStopCommandBuilder.build());
    if (RemoteCommandTypeEnum.MOBILE == remoteCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.MOBILE);
    }
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo =
        RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName("supervisor");
    moduleDelayInfo.setReceiveTime(remoteCommandVo.getReceiveTimeStamp().getTime());
    moduleDelayInfo.setTransitTime(remoteCommandVo.getTransitTimeStamp().getTime());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo.build());
    moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName(SERVER_NAME);
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);
    createBusinessRequest(remoteCommandVo, "EmergencyStop", chassisCommandBuilder.build());
    return HttpResult.success();
  }


  /**
   * <p>
   * Post as arrived command.
   * </p>
   */
  public HttpResult publishAsArrivedCommand(RemoteCommandVO remoteCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "remoteCommandVo");
    log.info("Receive as arrived command {}.", remoteCommandVo);
    if (!checkHermesConnection(remoteCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.AsArrivedCommand.Builder asArrivedRouteBuilder =
        RemoteServiceEntity.AsArrivedCommand.newBuilder();
    RemoteServiceEntity.MotionCommand.Builder motionCommandBuilder =
        RemoteServiceEntity.MotionCommand.newBuilder();
    motionCommandBuilder.setAsArrived(asArrivedRouteBuilder.build());
    createBusinessRequest(remoteCommandVo, "AsArrived", motionCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Post recovery command.
   * </p>
   */
  public HttpResult publishRecoveryCommand(RemoteCommandVO remoteCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "remoteCommandVo");
    log.info("Receive recovery command {}.", remoteCommandVo);
    if (!checkHermesConnection(remoteCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.VehicleEmergencyStopCommand.Builder emergencyStopCommandBuilder =
        RemoteServiceEntity.VehicleEmergencyStopCommand.newBuilder();
    emergencyStopCommandBuilder.setEmergencyStopCommand(false);
    RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder =
        RemoteServiceEntity.ChassisCommand.newBuilder();
    chassisCommandBuilder.setEnableCommand(false);
    if (RemoteCommandTypeEnum.MOBILE == remoteCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.MOBILE);
    }
    chassisCommandBuilder.setEmergencyStop(emergencyStopCommandBuilder.build());
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo =
        RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName("server-download");
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);
    createBusinessRequest(remoteCommandVo, "Recovery", chassisCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Post restart command.
   * </p>
   */
  public HttpResult publishRestartCommand(RemoteCommandVO remoteCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "remoteCommandVo");
    log.info("Receive restart command {}.", remoteCommandVo);
    if (!checkHermesConnection(remoteCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.ManualIntervention.Builder manualInterventionBuilder =
        RemoteServiceEntity.ManualIntervention.newBuilder();
    manualInterventionBuilder
        .setSourceType(RemoteServiceEntity.ManualIntervention.SourceType.REMOTE_CONTROL_PLATFORM);
    manualInterventionBuilder
        .setOperationType(RemoteServiceEntity.ManualIntervention.OperationType.RESTART);
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();
    headerBuilder.setModuleName("Restart");
    headerBuilder.setTimestamp(System.currentTimeMillis());
    Integer sequenceNum = 0;
    headerBuilder.setSequenceNum(sequenceNum);
    manualInterventionBuilder.setHeader(headerBuilder.build());
    RemoteServiceEntity.RemoteServiceRequest.Builder remoteServiceRequestBuilder =
        RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    remoteServiceRequestBuilder.setRemoteServiceType(
        RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.MANUAL_INTERVENTION);
    remoteServiceRequestBuilder.setManualIntervention(manualInterventionBuilder.build());
    createAndPublishDispatchResponse(remoteCommandVo.getVehicleName(), remoteServiceRequestBuilder.build());
    saveManualRestartCommand(remoteCommandVo.getVehicleName());
    return HttpResult.success();
  }

  /**
   * <p>
   * Post lamp control command.
   * </p>
   */
  public HttpResult publishLampControlCommand(RemoteCommandVO remoteCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "remoteCommandVo");
    log.info("Receive lamp control command {}.", remoteCommandVo);
    if (!checkHermesConnection(remoteCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.LightCtrlCommand.Builder lightControlCommandBuilder =
            RemoteServiceEntity.LightCtrlCommand.newBuilder();
    lightControlCommandBuilder.setLightToggle(true);
    RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder =
            RemoteServiceEntity.ChassisCommand.newBuilder();
    chassisCommandBuilder.setEnableCommand(true);
    chassisCommandBuilder.setHeadlightCmd(lightControlCommandBuilder.build());
    if (RemoteCommandTypeEnum.MOBILE == remoteCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.MOBILE);
    }
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo =
            RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName("server-download");
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);
    createBusinessRequest(remoteCommandVo, "ControlLamp", chassisCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Post remote control command.
   * </p>
   */
  public HttpResult publishRemoteControlCommand(RemoteControlCommandVO remoteControlCommandVo) {
    if (remoteControlCommandVo.getNeedResponse() == null) {
      remoteControlCommandVo.setNeedResponse(false);
    }
    ParameterCheckUtility.checkNotNull(remoteControlCommandVo, "remoteControlCommandVo");
    log.info("Receive remote control command {}.", remoteControlCommandVo);
    if (!checkHermesConnection(remoteControlCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.VehicleControlCommand.Builder vehicleControlCommandBuilder =
        RemoteServiceEntity.VehicleControlCommand.newBuilder();
    vehicleControlCommandBuilder
        .setTargetVelocity(remoteControlCommandVo.getTargetVelocity());
    vehicleControlCommandBuilder.setTargetAngle(remoteControlCommandVo.getTargetAngle());
    RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder =
        RemoteServiceEntity.ChassisCommand.newBuilder();
    if (RemoteCommandTypeEnum.NORMAL == remoteControlCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.NORMAL);
    } else if (RemoteCommandTypeEnum.SUPER == remoteControlCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.SUPER);
    } else if (RemoteCommandTypeEnum.MOBILE == remoteControlCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.MOBILE);
    } else {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.UNKNOWN);
    }
    chassisCommandBuilder.setSequenceNum(remoteControlCommandVo.getId());
    chassisCommandBuilder.setEnableCommand(true);
    chassisCommandBuilder.setVehicleControl(vehicleControlCommandBuilder.build());
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo =
        RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName(remoteControlCommandVo.getModuleName());
    moduleDelayInfo.setReceiveTime(remoteControlCommandVo.getReceiveTimeStamp().getTime());
    moduleDelayInfo.setTransitTime(remoteControlCommandVo.getTransitTimeStamp().getTime());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo.build());
    moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName(SERVER_NAME);
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);
    log.info("Send remote control command , time is {} and long {}, but id {}",
            remoteControlCommandVo.getReceiveTimeStamp().getTime(), System.currentTimeMillis(), remoteControlCommandVo.getId());
    createBusinessRequest(remoteControlCommandVo, "RemoteControl", chassisCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Publish reset abnormal command.
   * </p>
   */
  public HttpResult publishResetAbnormalCommand(RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteResetAbnormalCommandVo, "remoteResetAbnormalCommandVo");
    log.info("Receive reset abnormal command {}.", remoteResetAbnormalCommandVo);
    if (!checkHermesConnection(remoteResetAbnormalCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.ResetAbnormalRemoteContrlCommand.Builder resetAbnormalCommandBuilder =
        RemoteServiceEntity.ResetAbnormalRemoteContrlCommand.newBuilder();
    resetAbnormalCommandBuilder.setResetAbnormalCommand(true);
    RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder =
        RemoteServiceEntity.ChassisCommand.newBuilder();
    chassisCommandBuilder.setEnableCommand(true);
    chassisCommandBuilder.setResetAbnormal(resetAbnormalCommandBuilder.build());
    if (RemoteCommandTypeEnum.NORMAL == remoteResetAbnormalCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.NORMAL);
    } else if (RemoteCommandTypeEnum.SUPER == remoteResetAbnormalCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.SUPER);
    } else if (RemoteCommandTypeEnum.MOBILE == remoteResetAbnormalCommandVo.getCommandType()) {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.MOBILE);
    } else {
      chassisCommandBuilder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.UNKNOWN);
    }
    chassisCommandBuilder.setSequenceNum(remoteResetAbnormalCommandVo.getId());
    chassisCommandBuilder.setEnableCommand(true);
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo =
        RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName(remoteResetAbnormalCommandVo.getModuleName());
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo.build());
    moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName(SERVER_NAME);
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);
    createBusinessRequest(remoteResetAbnormalCommandVo, "ResetAbnormal", chassisCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Publish control traffic light command.
   * </p>
   */
  public HttpResult<Void> publishControlTrafficLightCommand(RemoteTrafficLightCommandVO remoteTrafficLightCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteTrafficLightCommandVo, "remoteTrafficLightCommandVo");
    log.info("Receive control traffic light command {}.", remoteTrafficLightCommandVo);
    if (!checkHermesConnection(remoteTrafficLightCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.TrafficLightCommand.Builder trafficLightCommandBuilder =
        RemoteServiceEntity.TrafficLightCommand.newBuilder();
    trafficLightCommandBuilder.addAllTrafficLightId(remoteTrafficLightCommandVo.getTrafficLightId());
    trafficLightCommandBuilder.setPassThrough(remoteTrafficLightCommandVo.getPassThrough());
    RemoteServiceEntity.MotionCommand.Builder motionCommandBuilder =
        RemoteServiceEntity.MotionCommand.newBuilder();
    motionCommandBuilder.setTrafficLightCommand(trafficLightCommandBuilder.build());
    createBusinessRequest(remoteTrafficLightCommandVo, "passTrafficLight", motionCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Publish relieves button stop command.
   * </p>
   */
  public HttpResult<Void> publishRelievesButtonStopCommand(RemoteCommandVO remoteCommandVo) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "remoteCommandVo");
    if (!checkHermesConnection(remoteCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.EstopButtonCommand.Builder eStopCommandBuilder =
            RemoteServiceEntity.EstopButtonCommand.newBuilder();
    eStopCommandBuilder.setReleaseEstop(true);
    RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder =
            RemoteServiceEntity.ChassisCommand.newBuilder();
    chassisCommandBuilder.setEnableCommand(true);
    chassisCommandBuilder.setEstopButton(eStopCommandBuilder.build());
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo =
            RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName("server-download");
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);
    createBusinessRequest(remoteCommandVo, "RelieveButtonStop", chassisCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * 通过无信号路口
   */
  public HttpResult passNoSignalIntersection(PassNoSignalIntersectionCommandVO vo) {
    ParameterCheckUtility.checkNotNull(vo, "PassNoSignalIntersectionCommandVO");
    log.info("Receive pass no signal intersection command {}.", vo);
    if (!checkHermesConnection(vo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.PassNoSignalIntersectionCommand.Builder trafficLightCommandBuilder =
            RemoteServiceEntity.PassNoSignalIntersectionCommand.newBuilder();
    trafficLightCommandBuilder.addAllGroupLaneId(vo.getGroupLaneIdList());
    trafficLightCommandBuilder.setPassThrough(Optional.ofNullable(vo.getPassThrough()).orElse(true));
    RemoteServiceEntity.MotionCommand.Builder motionCommandBuilder =
            RemoteServiceEntity.MotionCommand.newBuilder();
    motionCommandBuilder.setPassNoSignalIntersectionCommand(trafficLightCommandBuilder.build());
    createBusinessRequest(vo, "passNoSignalIntersection", motionCommandBuilder.build());
    return HttpResult.success();
  }

  /**
   * 切换灯
   */
  public HttpResult switchLight(SwitchLightCommandVO vo) {
    Assert.notNull(vo.getSwitchLight(), "切换操作不能为空!");
    Assert.notNull(vo.getSwitchType(), "切换类型不能为空!");
    log.info("Receive switch light command {}.", vo);
    if (!checkHermesConnection(vo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.SwitchLightCommand.Builder switchLightCommandBuilder =
            RemoteServiceEntity.SwitchLightCommand.newBuilder();
    SwitchTypeEnum switchTypeEnum = SwitchTypeEnum.valueOf(vo.getSwitchType());
    Assert.notNull(switchTypeEnum, "切换类型不能为空!");
    RemoteServiceEntity.SwitchLightCommand.SwitchLight switchLight = RemoteServiceEntity.SwitchLightCommand.SwitchLight.valueOf(vo.getSwitchLight());
    Assert.notNull(switchLight, "切换操作不能为空!");
    switch (switchTypeEnum) {
      case HEAD_LIGHT:
        switchLightCommandBuilder.setHeadLight(switchLight);
        break;
      case TURN_LEFT:
        switchLightCommandBuilder.setTurnLeft(switchLight);
        break;
      case TURN_RIGHT:
        switchLightCommandBuilder.setTurnRight(switchLight);
        break;
      case WARNING_FLASH:
        switchLightCommandBuilder.setWarningFlash(switchLight);
        break;
      default:
        Assert.notNull(switchLight, "切换操作不是指定类型!");
    }
    RemoteServiceEntity.ChassisCommand.Builder builder = RemoteServiceEntity.ChassisCommand.newBuilder();
    builder.setEnableCommand(true);
    builder.setSwitchLightCommand(switchLightCommandBuilder);
    builder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.NORMAL);

    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName("server-download");
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    builder.addModuleDelayInfo(moduleDelayInfo);

    createBusinessRequest(vo, "switchLight", builder.build());
    return HttpResult.success();
  }

  /**
   * 地图切换
   */
  public HttpResult runMapState(String operationType, RemoteCommandVO vo) {
    ParameterCheckUtility.checkNotNull(vo, "remoteCommandVo");
    log.info("Receive runMapState command {}.", vo);
    if (!checkHermesConnection(vo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.ManualIntervention.Builder manualBuilder = RemoteServiceEntity.ManualIntervention.newBuilder();
    manualBuilder.setSourceType(RemoteServiceEntity.ManualIntervention.SourceType.REMOTE_CONTROL_PLATFORM);
    if (Objects.equals(operationType, RemoteServiceEntity.ManualIntervention.OperationType.RUN_NO_MAP.name())) {
      manualBuilder.setOperationType(RemoteServiceEntity.ManualIntervention.OperationType.RUN_NO_MAP);
    } else if (Objects.equals(operationType, RemoteServiceEntity.ManualIntervention.OperationType.RUN_CALIBRATION.name())) {
      manualBuilder.setOperationType(RemoteServiceEntity.ManualIntervention.OperationType.RUN_CALIBRATION);
    } else {
      manualBuilder.setOperationType(RemoteServiceEntity.ManualIntervention.OperationType.RUN_HAVE_MAP);
    }
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();
    headerBuilder.setModuleName(operationType);
    headerBuilder.setTimestamp(System.currentTimeMillis());
    headerBuilder.setSequenceNum(0);
    headerBuilder.setProcessCallCount(Optional.ofNullable(vo.getRequestId()).orElse(RequestIdUtils.getRequestId()));
    headerBuilder.setDisableToSend(true);
    manualBuilder.setHeader(headerBuilder.build());
    RemoteServiceEntity.RemoteServiceRequest.Builder requestBuilder = RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    requestBuilder.setRemoteServiceType(RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.MANUAL_INTERVENTION);
    requestBuilder.setManualIntervention(manualBuilder.build());
    createAndPublishDispatchResponse(vo.getVehicleName(), requestBuilder.build());
    return HttpResult.success();
  }

  /**
   * <p>
   * Create business request.
   * </p>
   *
   * @param vo the name of vehicle
   * @param moduleName  the module name of header
   * @param command     the command
   */
  private void createBusinessRequest(RemoteCommandVO vo, String moduleName, Object command) {
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();

    headerBuilder.setModuleName(moduleName);
    headerBuilder.setTimestamp(System.currentTimeMillis());
    Integer sequenceNum = 0;
    headerBuilder.setSequenceNum(sequenceNum);
    headerBuilder.setDisableToSend(Optional.ofNullable(vo.getNeedResponse()).orElse(true));
    headerBuilder.setProcessCallCount(Optional.ofNullable(vo.getRequestId()).orElse(RequestIdUtils.getRequestId()));
    RemoteServiceEntity.BusinessRequest.Builder businessRequestBuilder =
        RemoteServiceEntity.BusinessRequest.newBuilder();
    if (command instanceof RemoteServiceEntity.NaviRoutingCommand) {
      businessRequestBuilder
          .setNaviRoutingCommand((RemoteServiceEntity.NaviRoutingCommand) command);
    } else if (command instanceof RemoteServiceEntity.MotionCommand) {
      businessRequestBuilder.setMotionCommand((RemoteServiceEntity.MotionCommand) command);
    } else if (command instanceof RemoteServiceEntity.ChassisCommand) {
      RemoteServiceEntity.ChassisCommand chassisCommand = (RemoteServiceEntity.ChassisCommand) command;
      businessRequestBuilder.setContinualChassisCommand(chassisCommand);
    } else {
      return;
    }
    businessRequestBuilder.setHeader(headerBuilder.build());
    RemoteServiceEntity.RemoteServiceRequest.Builder remoteServiceRequestBuilder =
        RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    remoteServiceRequestBuilder.setRemoteServiceType(
        RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.BUSINESS_REQUEST);
    remoteServiceRequestBuilder.setBusinessRequest(businessRequestBuilder.build());
    createAndPublishDispatchResponse(vo.getVehicleName(), remoteServiceRequestBuilder.build());
  }

  /**
   * <p>
   * Create and publish dispatch response.
   * </p>
   *
   * @param vehicleName          the vehicle name
   * @param remoteServiceRequest the remote service request.
   */
  private void createAndPublishDispatchResponse(String vehicleName,
                                                RemoteServiceEntity.RemoteServiceRequest remoteServiceRequest) {
    RemoteServiceEntity.DispatchResponse.Builder builder =
        RemoteServiceEntity.DispatchResponse.newBuilder();
    builder.setRemoteServiceRequest(remoteServiceRequest);

    RemoteServiceEntity.DispatchResponse response = builder.build();
    String responseString = response.toString();
    Client clientInfo = new Client();
    clientInfo.setName(vehicleName);
    CommandMessage commandMessage = new CommandMessage();
    commandMessage.setClient(clientInfo);
    commandMessage.setMessage(responseString);
    String topicName = RedisTopicConstant.SERVER_HERMES_PREFIX + vehicleName;
    RTopic rTopic = redissonClient.getTopic(topicName);
    rTopic.publish(JsonUtils.writeValueAsString(commandMessage));
    log.info("发送指令成功createAndPublishDispatchResponse={} listener ={}", commandMessage, rTopic.countSubscribers());
  }

  private boolean checkHermesConnection(String vehicleName) {
    String topicName = RedisTopicConstant.SERVER_HERMES_PREFIX + vehicleName;
    RTopic rTopic = redissonClient.getTopic(topicName);
    return rTopic.countSubscribers() > 0;
  }

  private void saveManualRestartCommand(String vehicleName) {
    VehicleManualRebootEntity rebootEntity = new VehicleManualRebootEntity();
    rebootEntity.setVehicleName(vehicleName);
    rebootEntity.setRecordTime(new Date());
    rebootEntity.setType(RebootTypeEnum.SUPERVISOR.getType());
    RedissonUtils.setObject(RedisCacheEnum.VEHICLE_RESTART_ROVER.getKey(vehicleName), rebootEntity, 300);
  }

  /**
   * 切换地图采集模式
   *
   * @param remoteCommandVO remoteCommandVO
   */
  public void switchMapCollection(RemoteServiceEntity.ManualIntervention.OperationType operationType, RemoteCommandVO remoteCommandVO) {
    // 校验hermes连接
    if (!checkHermesConnection(remoteCommandVO.getVehicleName())) {
      throw new BusinessException(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    // 组装指令数据
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();
    headerBuilder.setModuleName(operationType.name());
    headerBuilder.setSequenceNum(0);
    headerBuilder.setTimestamp(System.currentTimeMillis());
    headerBuilder.setDisableToSend(true);
    headerBuilder.setProcessCallCount(Optional.ofNullable(remoteCommandVO.getRequestId()).orElse(RequestIdUtils.getRequestId()));

    RemoteServiceEntity.ManualIntervention.Builder manualBuilder = RemoteServiceEntity.ManualIntervention.newBuilder();
    manualBuilder.setHeader(headerBuilder.build());
    manualBuilder.setSourceType(RemoteServiceEntity.ManualIntervention.SourceType.REMOTE_CONTROL_PLATFORM);
    manualBuilder.setOperationType(operationType);

    RemoteServiceEntity.RemoteServiceRequest.Builder requestBuilder = RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    requestBuilder.setRemoteServiceType(RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.MANUAL_INTERVENTION);
    requestBuilder.setManualIntervention(manualBuilder.build());
    // 下发
    createAndPublishDispatchResponse(remoteCommandVO.getVehicleName(), requestBuilder.build());
  }

  /**
   * 切换车辆模式
   * @param switchVehicleModeCommandVo 切换车辆模式命令
   */
  public HttpResult switchVehicleMode(SwitchVehicleModeCommandVO switchVehicleModeCommandVo) {
    ParameterCheckUtility.checkNotNull(switchVehicleModeCommandVo, "switchVehicleModeCommandVo");
    log.info("Receive switchVehicleMode command {}.", switchVehicleModeCommandVo);
    if (!checkHermesConnection(switchVehicleModeCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.ManualIntervention.Builder manualBuilder = RemoteServiceEntity.ManualIntervention.newBuilder();
    manualBuilder.setSourceType(RemoteServiceEntity.ManualIntervention.SourceType.REMOTE_CONTROL_PLATFORM);
    if (Objects.equals(switchVehicleModeCommandVo.getSwitchMode(), RemoteServiceEntity.ManualIntervention.OperationType.RUN_CALIBRATION.name())) {
      manualBuilder.setOperationType(RemoteServiceEntity.ManualIntervention.OperationType.RUN_CALIBRATION);
    } else {
      manualBuilder.setOperationType(RemoteServiceEntity.ManualIntervention.OperationType.RUN_HAVE_MAP);
    }
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();
    headerBuilder.setModuleName(SERVER_NAME);
    headerBuilder.setTimestamp(System.currentTimeMillis());
    headerBuilder.setSequenceNum(0);
    headerBuilder.setProcessCallCount(RequestIdUtils.getRequestId());
    headerBuilder.setDisableToSend(true);
    manualBuilder.setHeader(headerBuilder.build());
    RemoteServiceEntity.RemoteServiceRequest.Builder requestBuilder = RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    requestBuilder.setRemoteServiceType(RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.MANUAL_INTERVENTION);
    requestBuilder.setManualIntervention(manualBuilder.build());
    createAndPublishDispatchResponse(switchVehicleModeCommandVo.getVehicleName(), requestBuilder.build());
    return HttpResult.success();

  }

  /**
   * 下发车辆限速指令
   *
   * @param maxVelocityCommandVO 限速指令
   */
  public void publishMaxVelocity(MaxVelocityCommandVO maxVelocityCommandVO) {
      // 校验hermes连接
      log.info("Receive max velocity command {}.", maxVelocityCommandVO);
      if (!checkHermesConnection(maxVelocityCommandVO.getVehicleName())) {
          throw new BusinessException(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
      }

      // 组装指令数据
      RemoteServiceEntity.MaxVelocityCommand.Builder maxVelocityCommandBuilder = RemoteServiceEntity.MaxVelocityCommand.newBuilder();
      maxVelocityCommandBuilder.setMaxVelocity(maxVelocityCommandVO.getMaxVelocity());
      RemoteServiceEntity.ChassisCommand.Builder chassisCommandBuilder = RemoteServiceEntity.ChassisCommand.newBuilder();
      chassisCommandBuilder.setEnableCommand(true);
      chassisCommandBuilder.setMaxVelocityCommand(maxVelocityCommandBuilder.build());
      RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
      moduleDelayInfo.setModuleName("supervisor");
      moduleDelayInfo.setReceiveTime(maxVelocityCommandVO.getReceiveTimeStamp().getTime());
      moduleDelayInfo.setTransitTime(maxVelocityCommandVO.getTransitTimeStamp().getTime());
      chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo.build());
      moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
      moduleDelayInfo.setModuleName(SERVER_NAME);
      moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
      moduleDelayInfo.setTransitTime(System.currentTimeMillis());
      chassisCommandBuilder.addModuleDelayInfo(moduleDelayInfo);

      // 下发
      RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();
      headerBuilder.setModuleName("MaxVelocity");
      headerBuilder.setTimestamp(System.currentTimeMillis());
      headerBuilder.setSequenceNum(0);
      headerBuilder.setDisableToSend(Optional.ofNullable(maxVelocityCommandVO.getNeedResponse()).orElse(true));
      headerBuilder.setProcessCallCount(Optional.ofNullable(maxVelocityCommandVO.getRequestId()).orElse(RequestIdUtils.getRequestId()));
      RemoteServiceEntity.BusinessRequest.Builder businessRequestBuilder = RemoteServiceEntity.BusinessRequest.newBuilder();
      businessRequestBuilder.setHeader(headerBuilder.build());
      businessRequestBuilder.setChassisCommand(chassisCommandBuilder.build());
      RemoteServiceEntity.RemoteServiceRequest.Builder remoteServiceRequestBuilder = RemoteServiceEntity.RemoteServiceRequest.newBuilder();
      remoteServiceRequestBuilder.setRemoteServiceType(RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.BUSINESS_REQUEST);
      remoteServiceRequestBuilder.setBusinessRequest(businessRequestBuilder.build());
      createAndPublishDispatchResponse(maxVelocityCommandVO.getVehicleName(), remoteServiceRequestBuilder.build());
  }

  /**
   * 控制LocalView链路
   * @param localViewCtrlCommandVO LocalViewCtrlCommandVO
   */
  public void localViewCtrl(LocalViewCtrlCommandVO localViewCtrlCommandVO){

    log.info("Receive local view ctrl command {}.", localViewCtrlCommandVO);
    if (!checkHermesConnection(localViewCtrlCommandVO.getVehicleName())) {
      throw new BusinessException(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }

    //指令数据
    RemoteServiceEntity.LocalViewCtrlCommand.Builder localViewCtrlCommandBuilder = RemoteServiceEntity.LocalViewCtrlCommand.newBuilder();
    LocalViewCtrlType localViewCtrlType = LocalViewCtrlType.valueOf(localViewCtrlCommandVO.getLocalViewCtrlType());
    Assert.notNull(localViewCtrlType, "切换类型不能为空！");
    RemoteServiceEntity.LocalViewCtrlCommand.LocalViewCtrlType localViewCtrlTypeEnum = RemoteServiceEntity.LocalViewCtrlCommand.LocalViewCtrlType.valueOf(localViewCtrlType.name());
    localViewCtrlCommandBuilder.setLocalViewCtrlType(localViewCtrlTypeEnum);
    RemoteServiceEntity.ChassisCommand.Builder builder = RemoteServiceEntity.ChassisCommand.newBuilder();
    builder.setEnableCommand(true);
    builder.setLocalViewCtrlCommand(localViewCtrlCommandBuilder.build());
    builder.setCommandType(RemoteServiceEntity.ChassisCommand.CommandType.NORMAL);
    RemoteServiceEntity.ModuleDelayInfo.Builder moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName("server-localViewCtrl");
    moduleDelayInfo.setReceiveTime(localViewCtrlCommandVO.getReceiveTimeStamp().getTime());
    moduleDelayInfo.setTransitTime(localViewCtrlCommandVO.getTransitTimeStamp().getTime());
    builder.addModuleDelayInfo(moduleDelayInfo.build());
    moduleDelayInfo = RemoteServiceEntity.ModuleDelayInfo.newBuilder();
    moduleDelayInfo.setModuleName(SERVER_NAME);
    moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
    moduleDelayInfo.setTransitTime(System.currentTimeMillis());
    builder.addModuleDelayInfo(moduleDelayInfo);

    // 下发
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();
    headerBuilder.setModuleName("LocalViewCtrl");
    headerBuilder.setTimestamp(System.currentTimeMillis());
    headerBuilder.setSequenceNum(0);
    headerBuilder.setDisableToSend(Optional.ofNullable(localViewCtrlCommandVO.getNeedResponse()).orElse(true));
    headerBuilder.setProcessCallCount(Optional.ofNullable(localViewCtrlCommandVO.getRequestId()).orElse(RequestIdUtils.getRequestId()));
    RemoteServiceEntity.BusinessRequest.Builder businessRequestBuilder = RemoteServiceEntity.BusinessRequest.newBuilder();
    businessRequestBuilder.setHeader(headerBuilder.build());
    businessRequestBuilder.setChassisCommand(builder.build());
    RemoteServiceEntity.RemoteServiceRequest.Builder remoteServiceRequestBuilder = RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    remoteServiceRequestBuilder.setRemoteServiceType(RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.BUSINESS_REQUEST);
    remoteServiceRequestBuilder.setBusinessRequest(businessRequestBuilder.build());
    createAndPublishDispatchResponse(localViewCtrlCommandVO.getVehicleName(), remoteServiceRequestBuilder.build());
  }
}
