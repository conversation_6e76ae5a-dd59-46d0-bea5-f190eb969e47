package com.jdx.rover.server.stream.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.server.api.domain.vo.PassNoSignalIntersectionCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteTrafficLightCommandVO;
import com.jdx.rover.server.api.domain.vo.MaxVelocityCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchLightCommandVO;
import com.jdx.rover.server.api.domain.vo.LocalViewCtrlCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchVehicleModeCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import jdx.rover.idl.proto.RemoteServiceEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @description: RemoteCommandServiceImpl
 * @author: wangguotai
 * @create: 2024-09-26 21:38
 **/
@Service
@RequiredArgsConstructor
public class RemoteCommandServiceImpl extends AbstractProvider<RemoteCommandService> implements RemoteCommandService {

    private final com.jdx.rover.server.stream.service.RemoteCommandService remoteCommandService;

    @Override
    @ServiceInfo(name = "[急停]")
    public HttpResult<Void> publishEmergencyStopCommand(RemoteCommandVO remoteCommandVo) {
        return remoteCommandService.publishEmergencyStopCommand(remoteCommandVo);
    }

    @Override
    @ServiceInfo(name = "[视同到达]")
    public HttpResult<Void> publishAsArrivedCommand(RemoteCommandVO remoteCommandVo) {
        return remoteCommandService.publishAsArrivedCommand(remoteCommandVo);
    }

    @Override
    @ServiceInfo(name = "[恢复]")
    public HttpResult<Void> publishRecoveryCommand(RemoteCommandVO remoteCommandVo) {
        return remoteCommandService.publishRecoveryCommand(remoteCommandVo);
    }

    @Override
    @ServiceInfo(name = "[重启]")
    public HttpResult<Void> publishRestartCommand(RemoteCommandVO remoteCommandVo) {
        return remoteCommandService.publishRestartCommand(remoteCommandVo);
    }

    @Override
    @ServiceInfo(name = "[远程控制]")
    public HttpResult<Void> publishRemoteControlCommand(RemoteControlCommandVO remoteControlCommandVo) {
        return remoteCommandService.publishRemoteControlCommand(remoteControlCommandVo);
    }

    @Override
    @ServiceInfo(name = "[重置异常]")
    public HttpResult<Void> publishResetAbnormalCommand(RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo) {
        return remoteCommandService.publishResetAbnormalCommand(remoteResetAbnormalCommandVo);
    }

    @Override
    @ServiceInfo(name = "[红绿灯控制]")
    public HttpResult<Void> publishControlTrafficLightCommand(RemoteTrafficLightCommandVO remoteTrafficLightCommandVo) {
        return remoteCommandService.publishControlTrafficLightCommand(remoteTrafficLightCommandVo);
    }

    @Override
    @ServiceInfo(name = "[大灯控制]")
    public HttpResult<Void> publishControlLampCommand(RemoteCommandVO remoteCommandVO) {
        return remoteCommandService.publishLampControlCommand(remoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "[解除急停]")
    public HttpResult<Void> publishRelieveButtonStopCommand(RemoteCommandVO remoteCommandVO) {
        return remoteCommandService.publishRelievesButtonStopCommand(remoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "[通过无信号路口]")
    public HttpResult<Void> passNoSignalIntersection(PassNoSignalIntersectionCommandVO passNoSignalIntersectionCommandVO) {
        return remoteCommandService.passNoSignalIntersection(passNoSignalIntersectionCommandVO);
    }

    @Override
    @ServiceInfo(name = "[切换有图模式]")
    public HttpResult<Void> runHaveMap(RemoteCommandVO remoteCommandVO) {
        return remoteCommandService.runMapState(RemoteServiceEntity.ManualIntervention.OperationType.RUN_HAVE_MAP.name(), remoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "[切换无图模式]")
    public HttpResult<Void> runNoMap(RemoteCommandVO remoteCommandVO) {
        return remoteCommandService.runMapState(RemoteServiceEntity.ManualIntervention.OperationType.RUN_NO_MAP.name(), remoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "[灯光控制]")
    public HttpResult<Void> switchLight(SwitchLightCommandVO switchLightCommandVO) {
        return remoteCommandService.switchLight(switchLightCommandVO);
    }

    @Override
    @ServiceInfo(name = "[开启地图采集模式]")
    public HttpResult<Void> openMapCollection(RemoteCommandVO remoteCommandVO) {
        return JsfResponse.response(() -> remoteCommandService.switchMapCollection(RemoteServiceEntity.ManualIntervention.OperationType.RUN_MAP_COLLECTION, remoteCommandVO));
    }

    @Override
    @ServiceInfo(name = "[切换车辆模式]")
    public HttpResult<Void> switchVehicleMode(@NotNull(message = "请求参数不能为空") @Valid SwitchVehicleModeCommandVO switchVehicleModeCommandVO) {
        return remoteCommandService.switchVehicleMode(switchVehicleModeCommandVO);
    }

    @Override
    @ServiceInfo(name = "[下发车辆最大速度指令]")
    public HttpResult<Void> publishMaxVelocity(@NotNull(message = "请求参数不能为空") @Valid MaxVelocityCommandVO maxVelocityCommandVO) {
        return JsfResponse.response(() -> remoteCommandService.publishMaxVelocity(maxVelocityCommandVO));
    }

    @Override
    @ServiceInfo(name = "[控制LocalView链路]")
    public HttpResult<Void> localViewCtrl(@NotNull(message = "请求参数不能为空") @Valid LocalViewCtrlCommandVO localViewCtrlCommandVO) {
        return JsfResponse.response(() -> remoteCommandService.localViewCtrl(localViewCtrlCommandVO));
    }
}