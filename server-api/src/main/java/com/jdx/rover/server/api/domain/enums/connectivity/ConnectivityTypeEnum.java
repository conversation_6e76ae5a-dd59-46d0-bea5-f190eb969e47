/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.api.domain.enums.connectivity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 连通性类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ConnectivityTypeEnum {
    /**
     * 未知ping
     */
    UNKNOWN_PING(0, "未知ping", 0f),
    
    /**
     * 域控双端ping ***********
     */
    DOMAIN_ROUTER(1, "域控双端ping ***********", 10f),
    
    /**
     * 域控102端ping jdxgateway.jdl.cn
     */
    DOMAIN_WEB(2, "域控102端ping jdxgateway.jdl.cn", 100f),
    
    /**
     * 域控102端ping 雷达IP
     */
    DOMAIN_LIDAR(3, "域控102端ping 雷达IP", 10f),
    
    /**
     * 域控双端ping *************
     */
    DOMAIN_ANDROID(4, "域控双端ping *************", 10f),
    
    /**
     * 安卓104端ping jdxgateway.jdl.cn
     */
    ANDROID_WEB(5, "安卓104端ping jdxgateway.jdl.cn", 100f);

    /**
     * 连通性类型值
     */
    private final Integer value;
    
    /**
     * 标题描述
     */
    private final String title;
    
    /**
     * 延时阈值（毫秒）
     */
    private final Float delayThreshold;

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static ConnectivityTypeEnum of(final Integer value) {
        if (value == null) {
            return null;
        }
        for (ConnectivityTypeEnum itemEnum : ConnectivityTypeEnum.values()) {
            if (itemEnum.value.equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
    
    /**
     * 是否需要告警检查
     *
     * @return true-需要检查，false-不需要检查
     */
    public boolean needAlarmCheck() {
        return this != UNKNOWN_PING && this != ANDROID_WEB;
    }
}
